using Godot;

namespace SogniSpade3D.Game
{
    /// <summary>
    /// Main menu controller for Sogni & Spade 3D
    /// Handles navigation between scenarios and game options
    /// </summary>
    public partial class MainMenu : Control
    {
        // Main menu buttons
        private Button startButton;
        private Button loadButton;
        private Button scenarioButton;
        private Button optionsButton;
        private Button exitButton;

        // Scenario menu
        private Control scenarioMenu;
        private Button valleyButton;
        private Button dawnButton;
        private Button greywoodButton;
        private Button backButton;

        public override void _Ready()
        {
            GD.Print("=== SOGNI & SPADE 3D MAIN MENU ===");
            GD.Print("Benvenuto nel menu principale!");

            // Get main menu button references
            startButton = GetNode<Button>("VBoxContainer/MenuButtons/StartButton");
            loadButton = GetNode<Button>("VBoxContainer/MenuButtons/LoadButton");
            scenarioButton = GetNode<Button>("VBoxContainer/MenuButtons/ScenarioButton");
            optionsButton = GetNode<Button>("VBoxContainer/MenuButtons/OptionsButton");
            exitButton = GetNode<Button>("VBoxContainer/MenuButtons/ExitButton");

            // Get scenario menu references
            scenarioMenu = GetNode<Control>("ScenarioMenu");
            valleyButton = GetNode<Button>("ScenarioMenu/ScenarioPanel/ScenarioVBox/ValleyButton");
            dawnButton = GetNode<Button>("ScenarioMenu/ScenarioPanel/ScenarioVBox/DawnButton");
            greywoodButton = GetNode<Button>("ScenarioMenu/ScenarioPanel/ScenarioVBox/GreywoodButton");
            backButton = GetNode<Button>("ScenarioMenu/ScenarioPanel/ScenarioVBox/BackButton");

            // Connect main menu button signals
            startButton.Pressed += OnStartPressed;
            loadButton.Pressed += OnLoadPressed;
            scenarioButton.Pressed += OnScenarioPressed;
            optionsButton.Pressed += OnOptionsPressed;
            exitButton.Pressed += OnExitPressed;

            // Connect scenario menu button signals
            valleyButton.Pressed += OnValleyPressed;
            dawnButton.Pressed += OnDawnPressed;
            greywoodButton.Pressed += OnGreywoodPressed;
            backButton.Pressed += OnBackPressed;

            // Initialize menu state
            scenarioMenu.Visible = false;

            // Play main theme music
            PlayMainTheme();
        }

        private void PlayMainTheme()
        {
            // Create a simple audio player for the main theme
            var audioPlayer = new AudioStreamPlayer();
            AddChild(audioPlayer);

            // In a full implementation, this would load the actual main theme
            GD.Print("Playing main theme music...");
        }

        private void OnStartPressed()
        {
            GD.Print("Iniziando nuovo gioco... (Starting new game...)");

            // Start with Valley of Doom scenario
            LoadScenario("ValleyOfDoomEnhanced");
        }

        private void OnLoadPressed()
        {
            GD.Print("Caricando gioco salvato... (Loading saved game...)");

            // Try to load the autosave
            var gameManager = new GameManager();
            if (gameManager.LoadGame("autosave"))
            {
                GD.Print("Gioco caricato con successo! (Game loaded successfully!)");
                // Load the saved scenario
                LoadScenario("ValleyOfDoomEnhanced"); // Default to Valley of Doom
            }
            else
            {
                GD.Print("Nessun salvataggio trovato (No save found)");
                ShowErrorDialog("Nessun salvataggio trovato.\nSeleziona 'Inizia Gioco' per iniziare una nuova partita.\n\nNo save found.\nSelect 'Start Game' to begin a new game.");
            }
        }

        private void OnScenarioPressed()
        {
            GD.Print("Aprendo menu scenari... (Opening scenario menu...)");
            scenarioMenu.Visible = true;
        }

        private void OnOptionsPressed()
        {
            GD.Print("Aprendo opzioni... (Opening options...)");
            ShowOptionsDialog();
        }

        private void OnExitPressed()
        {
            GD.Print("Uscendo dal gioco... (Exiting game...)");
            GetTree().Quit();
        }

        // Scenario menu handlers
        private void OnValleyPressed()
        {
            GD.Print("Caricando Valle della Perdizione... (Loading Valley of Doom...)");
            LoadScenario("ValleyOfDoomEnhanced");
        }

        private void OnDawnPressed()
        {
            GD.Print("Caricando Ritorno all'Alba... (Loading Return to Dawn...)");
            LoadScenario("ReturnToDawn");
        }

        private void OnGreywoodPressed()
        {
            GD.Print("Caricando Segreto di Greywood... (Loading Secret of Greywood...)");
            LoadScenario("SecretOfGreywood");
        }

        private void OnBackPressed()
        {
            GD.Print("Tornando al menu principale... (Returning to main menu...)");
            scenarioMenu.Visible = false;
        }

        private void LoadScenario(string scenarioName)
        {
            string scenarioPath = $"res://Game/Scenes/{scenarioName}.tscn";

            if (ResourceLoader.Exists(scenarioPath))
            {
                GD.Print($"Loading scenario: {scenarioName}");

                try
                {
                    // Force garbage collection before scene transition to prevent assembly issues
                    GC.Collect();
                    GC.WaitForPendingFinalizers();

                    // Preload the scene resource to ensure assemblies are available
                    var sceneResource = GD.Load<PackedScene>(scenarioPath);
                    if (sceneResource != null)
                    {
                        // Use CallDeferred with preloaded resource to prevent assembly loading issues
                        GetTree().CallDeferred("change_scene_to_packed", sceneResource);
                        GD.Print("Scene transition initiated successfully with preloaded resource");
                    }
                    else
                    {
                        // Fallback to file-based loading
                        GetTree().CallDeferred("change_scene_to_file", scenarioPath);
                        GD.Print("Scene transition initiated with file path fallback");
                    }
                }
                catch (System.Exception ex)
                {
                    GD.PrintErr($"Error during scene transition: {ex.Message}");
                    ShowErrorDialog($"Errore nel caricamento: {ex.Message}");
                }
            }
            else
            {
                GD.PrintErr($"Scenario not found: {scenarioPath}");
                ShowErrorDialog($"Scenario non trovato: {scenarioName}");
            }
        }

        private void ShowOptionsDialog()
        {
            // Create a simple options dialog
            var dialog = new AcceptDialog();
            dialog.Title = "Opzioni (Options)";
            dialog.DialogText = "Opzioni di gioco:\n\n" +
                               "Lingua: Italiano/English\n" +
                               "Volume Musica: 100%\n" +
                               "Volume Effetti: 100%\n" +
                               "Risoluzione: 1920x1080\n" +
                               "Modalità Schermo: Finestra\n\n" +
                               "Game Options:\n\n" +
                               "Language: Italian/English\n" +
                               "Music Volume: 100%\n" +
                               "SFX Volume: 100%\n" +
                               "Resolution: 1920x1080\n" +
                               "Screen Mode: Windowed";

            AddChild(dialog);
            dialog.PopupCentered();

            // Auto-remove dialog when closed
            dialog.TreeExited += () => dialog.QueueFree();
        }

        private void ShowErrorDialog(string message)
        {
            var dialog = new AcceptDialog();
            dialog.Title = "Errore (Error)";
            dialog.DialogText = message;

            AddChild(dialog);
            dialog.PopupCentered();

            // Auto-remove dialog when closed
            dialog.TreeExited += () => dialog.QueueFree();
        }

        public override void _Input(InputEvent @event)
        {
            // Handle escape key to close scenario menu
            if (@event.IsActionPressed("ui_cancel") && scenarioMenu.Visible)
            {
                OnBackPressed();
            }
        }

        public override void _ExitTree()
        {
            GD.Print("Leaving main menu...");
        }
    }
}
